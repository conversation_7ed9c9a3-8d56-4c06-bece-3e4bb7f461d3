# 需求 [fix_addr]

## 反馈

1. Fred反馈房源C12253587，为什么地址显示被修改了。我记得说过要显示original地址的。另外这个Placeway为什么被删除了？
  origAddr: '4 Stonedale Placeway, Toronto, ON M3B 1W3',
  addr: '4 Stonedale',

## 需求提出人:    Fred

## 修改人：       Lu<PERSON> xiaowei

## 提出日期:      2025-07-10

## 原因

1. Placeway被删除是由于源数据提供的st_sfx在format时没有获取到正确的缩写,对没有获取到缩写的st_sfx处理存在问题。

## 解决办法

1. 修改address format时对st_sfx的处理逻辑。
2. 由于origAddr包含city,prov,unit等信息,不修改前端显示逻辑。eg:#208 - 70 Port Street, Mississauga, ON L5G 4V8

## 影响范围

1. 源数据提供st_sfx/st_dir的处理

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-07-10

## online-step

1. 重启watch&import
2. 修改房源C12253587数据
```shell
db.properties.updateOne({_id:'TRBC12253587'},{$set:{addr:'4 Stonedale Plwy',st_sfx:'Plwy'}})
```
