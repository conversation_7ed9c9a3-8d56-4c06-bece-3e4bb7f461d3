should = require('should')
testHelpers = require('../00_common/helpers')
helpersStringLib = require('../../built/lib/helpers_string')

# ./mate4.sh compile
# ./test.sh -f lib/helpers_string.js

describe 'Helpers function tests',->
  before (done) ->
    @timeout(300000)
    done()

  describe 'isEmail',->
    tests=[
      {
        input:'farid142@hotmaiñ.com',
        output:false
      },
      {
        input:'<EMAIL>'
        output: false,
      },
      {
        input:'<EMAIL>'
        output: true,
      }
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'.<EMAIL>'
        output: false
      },
      {
        input:'<EMAIL>',
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'username@sina'
        output: false
      }
      {
        input:'username@<EMAIL>',
        output: false
      },
      {
        input:'username@@yeah.com',
        output: false
      },
      {
        input: '<EMAIL>@',
        output: false
      },
      {
        input:'<EMAIL>.'
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'username@.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'7409489😄<EMAIL>'
        output: false
      },
      {
        input:'740948991@qq😄.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'username@.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'7409489😄<EMAIL>'
        output: false
      },
      {
        input:'740948991@qq😄.com'
        output: false
      },
      {
        input:'<EMAIL>'
        output: false
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      },
      {
        input:'<EMAIL>'
        output: true
      }
    ]
    tests.forEach (test)->
      it "should conver #{test.input} to #{test.output}", (done)->
        output = helpersStringLib.isEmail test.input
        should.equal(output, test.output)
        done()
